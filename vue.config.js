"use strict";
const path = require("path");
const { title, abbreviation, devPort } = require("./src/config/settings");
const pkg = require("./package.json");
const Webpack = require("webpack");
const WebpackBar = require("webpackbar");
const FileManagerPlugin = require("filemanager-webpack-plugin");
const date = require("dayjs")().format("YYYY_M_D");
const time = require("dayjs")().format("YYYY-M-D HH:mm:ss");
const CompressionWebpackPlugin = require("compression-webpack-plugin");
const productionGzipExtensions = ["html", "js", "css", "svg"];
const UglifyJsPlugin = require("uglifyjs-webpack-plugin"); //引入插件
const SpeedMeasurePlugin = require("speed-measure-webpack-plugin");
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
function resolve(dir) {
  return path.join(__dirname, dir);
}

function mockServer() {
  if (process.env.NODE_ENV === "development") {
    const mockServer = require("./mock/mock-server.js");
    return mockServer;
  } else {
    return "";
  }
}

const name = title || "ilz";

const os = require('os');
// cpu核数
const threads = os.cpus().length;

const compress = new CompressionWebpackPlugin(
  {
    filename: info => {
      return `${info.path}.gz${info.query}`
    },
    algorithm: 'gzip',
    threshold: 10240,
    test: /\.(js|css|json|txt|html|ico|svg)(\?.*)?$/i,
    minRatio: 0.8,
    deleteOriginalAssets: false
  }
)

module.exports = {
  publicPath: process.env.BASE_URL,
  productionSourceMap: false,
  assetsDir: "static",
  outputDir: "dist",
  lintOnSave: false,
  transpileDependencies: ["vue-echarts", "resize-detector"],

  devServer: {
    hot: true,
    port: devPort,
    open: true,
    noInfo: false,
    overlay: {
      warnings: true,
      errors: true,
    },
    proxy: {
      // change xxx-api/login => mock/login
      // detail: https://cli.vuejs.org/config/#devserver-proxy

      [process.env.VUE_APP_BASE_API]: {
        // target: "http://localhost:8081/base", //代表履职配置
        target: "http://*************:8081/base", //代表履职配置
        //  target: "http://localhost:8082", //建议议案配置
        // target: "https://rdtest.rd.gz.cn/base2",
        changeOrigin: true,
        pathRewrite: {
          ["^" + process.env.VUE_APP_BASE_API]: "",
        },
        api: {
          target: "http://************:8081/base",
          changeOrigin: true,
          pathRewrite: {
            ["^api"]: "",
          },
        },
      },
    },
    after: mockServer(),
    historyApiFallback: true,
  },

  configureWebpack: {
    plugins: [
      new Webpack.ProvidePlugin({
        "window.Quill": "quill/dist/quill.js",
        Quill: "quill/dist/quill.js",
      }),
    ],
    cache: {
      // 使用文件缓存，加速二次构建
      type: 'filesystem'
    },
    optimization: {
      minimizer: [
        new UglifyJsPlugin({
          uglifyOptions: {
            // 删除注释
            output: {
              comments: false,
            },
            warnings: false,
            // 删除console debugger 删除警告
            compress: {
              drop_console: true, //console
              drop_debugger: false,
              pure_funcs: ["console.log"], //移除console
            },
          },
        }),
      ],
    },
    resolve: {
      alias: {
        "@": resolve("src"),
      },
    },
  },

  chainWebpack: (config) => {
    const oneOfsMap = config.module.rule("scss").oneOfs.store;
    // 取消首屏加载就loadind全部页面
    config.plugins.delete('prefetch');
    oneOfsMap.forEach((item) => {
      item
        .use("sass-resources-loader")
        .loader("sass-resources-loader")
        .options({
          // Provide path to the file with resources
          // 要公用的scss的路径
          resources: "src/styles/mixin.scss",
        })
        .end();
    });
    // 多核编译
    config.module
      .rule('vue')
      .use('thread-loader')
      .loader('thread-loader')
      .options({
        workers: threads
      })
      .end();
    config.module
      .rule('js')
      .use('thread-loader')
      .loader('thread-loader')
      .options({
        workers: threads
      })
      .end();

    config.when(process.env.NODE_ENV === "production", (config) => {

      config.optimization.minimizer('terser').tap((args) => {
        args[0].parallel = threads;
        args[0].terserOptions.compress.warnings = true;
        args[0].terserOptions.compress.drop_debugger = true;
        args[0].terserOptions.compress.drop_console = true;
        return args;
      });
      config.plugin("BundleAnalyzerPlugin").use(BundleAnalyzerPlugin)
      config.plugin("SpeedMeasurePlugin").use(SpeedMeasurePlugin)
      config.plugin("compress").use(compress)
    });
  },

  runtimeCompiler: true,
  css: {
    requireModuleExtension: true,
    sourceMap: true,
    loaderOptions: {
      scss: {
        prependData: '@import "~@/styles/variables.scss";',
      },
    },
  },
};
