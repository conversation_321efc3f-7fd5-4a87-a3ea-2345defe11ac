import { instance_1 } from "@/api/axiosRq";

// 获取制度管理列表
export function getSystemList(params) {
  return instance_1({
    url: "/systemManagement/getSystemList",
    method: "get",
    params,
  });
}

// 创建制度
export function createSystem(data) {
  return instance_1({
    url: "/systemManagement/createSystem",
    method: "post",
    data,
  });
}

// 更新制度
export function updateSystem(data) {
  return instance_1({
    url: "/systemManagement/updateSystem",
    method: "put",
    data,
  });
}

// 删除制度
export function deleteSystem(id) {
  return instance_1({
    url: `/systemManagement/deleteSystem/${id}`,
    method: "delete",
  });
}

// 获取制度详情
export function getSystemDetail(id) {
  return instance_1({
    url: `/systemManagement/${id}`,
    method: "get",
  });
}

// 上传制度附件
export function uploadSystemAttachment(data) {
  return instance_1({
    url: "/systemManagement/fileUpload",
    method: "post",
    headers: {
      "Content-Type": "multipart/form-data",
    },
    data,
  });
}

// 删除制度附件
export function deleteSystemAttachment(id) {
  return instance_1({
    url: `/systemManagement/deleteAttachment/${id}`,
    method: "delete",
  });
}

// 下载制度附件
export function downloadSystemAttachment(id) {
  return instance_1({
    url: `/systemManagement/downloadSystemAttachment/${id}`,
    method: "get",
    responseType: "blob",
  });
}
