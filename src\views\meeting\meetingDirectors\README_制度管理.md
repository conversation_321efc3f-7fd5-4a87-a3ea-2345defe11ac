# 制度管理页面

## 功能概述

制度管理页面提供了完整的制度信息管理功能，包括：

- 制度列表展示
- 制度名称搜索
- 新增制度
- 编辑制度
- 查看制度详情
- 删除制度
- 制度附件上传和下载

## 页面结构

### 文件位置
- 页面文件：`src/views/meeting/meetingDirectors/systemManagement.vue`
- API文件：`src/api/systemManagement.js`
- 路由配置：`src/router/modules/jointMeeting.js`

### 路由信息
- 路径：`/ComponentData/systemManagement`
- 名称：`systemManagement`
- 标题：制度管理

## 功能详情

### 1. 列表展示
- 显示制度名称、制度描述、附件名称、创建时间
- 支持分页显示
- 序号自动计算

### 2. 搜索功能
- 支持按制度名称模糊搜索
- 实时搜索，支持回车键搜索

### 3. 新增制度
- 制度名称（必填，最多100字符）
- 制度描述（可选，最多500字符）
- 制度附件（可选，支持PDF、Word、Excel格式，最大20MB）

### 4. 编辑制度
- 可修改制度名称、描述和附件
- 保留原有数据进行编辑

### 5. 查看制度
- 只读模式查看制度详情
- 可下载附件

### 6. 删除制度
- 确认删除提示
- 删除后自动刷新列表

### 7. 附件管理
- 支持上传PDF、Word、Excel格式文件
- 文件大小限制20MB
- 支持附件下载
- 上传前文件格式和大小验证

## 技术实现

### 组件依赖
- `SearchForm`：搜索表单组件
- `SingleSearch`：单项搜索组件
- `a-table`：Ant Design表格组件
- `a-modal`：模态框组件
- `a-upload`：文件上传组件

### API接口
- `getSystemList`：获取制度列表
- `createSystem`：创建制度
- `updateSystem`：更新制度
- `deleteSystem`：删除制度
- `downloadSystemAttachment`：下载附件
- `fileUpload`：文件上传

### 数据结构
```javascript
{
  id: Number,              // 制度ID
  systemName: String,      // 制度名称
  description: String,     // 制度描述
  attachmentId: String,    // 附件ID
  attachmentName: String,  // 附件名称
  attachmentPath: String,  // 附件路径
  createTime: String       // 创建时间
}
```

## 使用说明

### 访问页面
1. 登录系统
2. 导航到"主任会议组成成员联系代表"模块
3. 点击"制度管理"菜单项

### 新增制度
1. 点击"新增制度"按钮
2. 填写制度名称（必填）
3. 填写制度描述（可选）
4. 上传制度附件（可选）
5. 点击"确定"保存

### 编辑制度
1. 在列表中找到要编辑的制度
2. 点击"编辑"按钮
3. 修改相关信息
4. 点击"确定"保存

### 删除制度
1. 在列表中找到要删除的制度
2. 点击"删除"按钮
3. 确认删除操作

### 下载附件
1. 在列表中找到有附件的制度
2. 点击"下载附件"按钮
3. 文件将自动下载到本地

## 注意事项

1. 制度名称为必填项，不能为空
2. 附件上传支持PDF、Word、Excel格式
3. 单个附件大小不能超过20MB
4. 删除制度操作不可恢复，请谨慎操作
5. **前端代码已完成，已连接真实API接口**

## 开发状态

✅ **前端代码已完成**
- 已启用真实API调用
- 已移除模拟数据代码
- API接口已正确配置
- 文件上传功能已实现

## API测试

为了方便测试API接口，已创建测试页面：
- 测试页面路径：`/ComponentData/systemManagementTest`
- 可以测试所有API接口功能
- 包含错误处理和响应时间统计

## 维护说明

- 定期检查文件上传功能
- 监控附件存储空间
- 定期备份制度数据
- 根据业务需求调整字段验证规则
