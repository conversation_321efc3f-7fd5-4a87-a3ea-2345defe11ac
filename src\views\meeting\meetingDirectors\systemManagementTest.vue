<template>
  <div class="test-container">
    <h2>制度管理API测试页面</h2>
    
    <div class="test-section">
      <h3>1. 测试获取制度列表</h3>
      <a-button type="primary" @click="testGetList" :loading="loading.getList">
        测试获取列表
      </a-button>
      <div v-if="results.getList" class="result-box">
        <pre>{{ JSON.stringify(results.getList, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h3>2. 测试创建制度</h3>
      <a-form layout="inline">
        <a-form-item label="制度名称">
          <a-input v-model="testData.systemName" placeholder="请输入制度名称" />
        </a-form-item>
        <a-form-item label="制度描述">
          <a-input v-model="testData.description" placeholder="请输入制度描述" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="testCreate" :loading="loading.create">
            测试创建
          </a-button>
        </a-form-item>
      </a-form>
      <div v-if="results.create" class="result-box">
        <pre>{{ JSON.stringify(results.create, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h3>3. 测试更新制度</h3>
      <a-form layout="inline">
        <a-form-item label="制度ID">
          <a-input-number v-model="testData.updateId" placeholder="请输入制度ID" />
        </a-form-item>
        <a-form-item label="新名称">
          <a-input v-model="testData.newName" placeholder="请输入新名称" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="testUpdate" :loading="loading.update">
            测试更新
          </a-button>
        </a-form-item>
      </a-form>
      <div v-if="results.update" class="result-box">
        <pre>{{ JSON.stringify(results.update, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h3>4. 测试删除制度</h3>
      <a-form layout="inline">
        <a-form-item label="制度ID">
          <a-input-number v-model="testData.deleteId" placeholder="请输入要删除的制度ID" />
        </a-form-item>
        <a-form-item>
          <a-button type="danger" @click="testDelete" :loading="loading.delete">
            测试删除
          </a-button>
        </a-form-item>
      </a-form>
      <div v-if="results.delete" class="result-box">
        <pre>{{ JSON.stringify(results.delete, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h3>5. 测试文件上传</h3>
      <a-upload
        :before-upload="beforeUpload"
        @change="handleUploadChange"
        :file-list="fileList"
        accept=".pdf,.doc,.docx,.xls,.xlsx"
      >
        <a-button :loading="loading.upload">
          <a-icon type="upload" />
          选择文件测试上传
        </a-button>
      </a-upload>
      <div v-if="results.upload" class="result-box">
        <pre>{{ JSON.stringify(results.upload, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h3>测试结果汇总</h3>
      <a-table
        :columns="summaryColumns"
        :data-source="summaryData"
        :pagination="false"
        size="small"
      />
    </div>
  </div>
</template>

<script>
import {
  getSystemList,
  createSystem,
  updateSystem,
  deleteSystem,
} from "@/api/systemManagement";
import { fileUpload } from "@/api/commonApi/file";

export default {
  name: "SystemManagementTest",
  data() {
    return {
      loading: {
        getList: false,
        create: false,
        update: false,
        delete: false,
        upload: false,
      },
      results: {
        getList: null,
        create: null,
        update: null,
        delete: null,
        upload: null,
      },
      testData: {
        systemName: "测试制度",
        description: "这是一个测试制度的描述",
        updateId: null,
        newName: "更新后的制度名称",
        deleteId: null,
      },
      fileList: [],
      summaryColumns: [
        {
          title: "测试项目",
          dataIndex: "test",
          key: "test",
        },
        {
          title: "状态",
          dataIndex: "status",
          key: "status",
          customRender: (text) => {
            if (text === "success") {
              return <a-tag color="green">成功</a-tag>;
            } else if (text === "error") {
              return <a-tag color="red">失败</a-tag>;
            } else {
              return <a-tag color="gray">未测试</a-tag>;
            }
          },
        },
        {
          title: "响应时间",
          dataIndex: "time",
          key: "time",
        },
        {
          title: "错误信息",
          dataIndex: "error",
          key: "error",
        },
      ],
      summaryData: [
        { key: "1", test: "获取制度列表", status: "pending", time: "-", error: "-" },
        { key: "2", test: "创建制度", status: "pending", time: "-", error: "-" },
        { key: "3", test: "更新制度", status: "pending", time: "-", error: "-" },
        { key: "4", test: "删除制度", status: "pending", time: "-", error: "-" },
        { key: "5", test: "文件上传", status: "pending", time: "-", error: "-" },
      ],
    };
  },
  methods: {
    // 测试获取列表
    async testGetList() {
      this.loading.getList = true;
      const startTime = Date.now();
      
      try {
        const response = await getSystemList({
          pageNum: 1,
          pageSize: 10,
          systemName: "",
        });
        
        const endTime = Date.now();
        this.results.getList = response.data;
        this.updateSummary("获取制度列表", "success", `${endTime - startTime}ms`);
        this.$message.success("获取列表测试成功");
      } catch (error) {
        const endTime = Date.now();
        this.results.getList = { error: error.message };
        this.updateSummary("获取制度列表", "error", `${endTime - startTime}ms`, error.message);
        this.$message.error("获取列表测试失败: " + error.message);
      } finally {
        this.loading.getList = false;
      }
    },

    // 测试创建制度
    async testCreate() {
      if (!this.testData.systemName) {
        this.$message.warning("请输入制度名称");
        return;
      }
      
      this.loading.create = true;
      const startTime = Date.now();
      
      try {
        const response = await createSystem({
          systemName: this.testData.systemName,
          description: this.testData.description,
        });
        
        const endTime = Date.now();
        this.results.create = response.data;
        this.updateSummary("创建制度", "success", `${endTime - startTime}ms`);
        this.$message.success("创建制度测试成功");
      } catch (error) {
        const endTime = Date.now();
        this.results.create = { error: error.message };
        this.updateSummary("创建制度", "error", `${endTime - startTime}ms`, error.message);
        this.$message.error("创建制度测试失败: " + error.message);
      } finally {
        this.loading.create = false;
      }
    },

    // 测试更新制度
    async testUpdate() {
      if (!this.testData.updateId) {
        this.$message.warning("请输入制度ID");
        return;
      }
      
      this.loading.update = true;
      const startTime = Date.now();
      
      try {
        const response = await updateSystem({
          id: this.testData.updateId,
          systemName: this.testData.newName,
          description: "更新后的描述",
        });
        
        const endTime = Date.now();
        this.results.update = response.data;
        this.updateSummary("更新制度", "success", `${endTime - startTime}ms`);
        this.$message.success("更新制度测试成功");
      } catch (error) {
        const endTime = Date.now();
        this.results.update = { error: error.message };
        this.updateSummary("更新制度", "error", `${endTime - startTime}ms`, error.message);
        this.$message.error("更新制度测试失败: " + error.message);
      } finally {
        this.loading.update = false;
      }
    },

    // 测试删除制度
    async testDelete() {
      if (!this.testData.deleteId) {
        this.$message.warning("请输入制度ID");
        return;
      }
      
      this.loading.delete = true;
      const startTime = Date.now();
      
      try {
        const response = await deleteSystem(this.testData.deleteId);
        
        const endTime = Date.now();
        this.results.delete = response.data;
        this.updateSummary("删除制度", "success", `${endTime - startTime}ms`);
        this.$message.success("删除制度测试成功");
      } catch (error) {
        const endTime = Date.now();
        this.results.delete = { error: error.message };
        this.updateSummary("删除制度", "error", `${endTime - startTime}ms`, error.message);
        this.$message.error("删除制度测试失败: " + error.message);
      } finally {
        this.loading.delete = false;
      }
    },

    // 文件上传前验证
    beforeUpload(file) {
      const isValidType = [
        "application/pdf",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ].includes(file.type);

      if (!isValidType) {
        this.$message.error("只能上传 PDF、Word、Excel 格式的文件！");
        return false;
      }

      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("文件大小不能超过 20MB！");
        return false;
      }

      return false; // 阻止自动上传，手动处理
    },

    // 文件上传变化
    handleUploadChange(info) {
      if (info.file.originFileObj) {
        this.uploadFile(info.file.originFileObj);
      }
    },

    // 上传文件
    async uploadFile(file) {
      this.loading.upload = true;
      const startTime = Date.now();
      
      try {
        const formData = new FormData();
        formData.append("file", file);
        formData.append("type", "10"); // 制度管理附件类型

        const response = await fileUpload(formData);
        
        const endTime = Date.now();
        this.results.upload = response.data;
        this.updateSummary("文件上传", "success", `${endTime - startTime}ms`);
        this.$message.success("文件上传测试成功");
        
        this.fileList = [
          {
            uid: response.data.data[0].attId,
            name: response.data.data[0].originalName,
            status: "done",
          },
        ];
      } catch (error) {
        const endTime = Date.now();
        this.results.upload = { error: error.message };
        this.updateSummary("文件上传", "error", `${endTime - startTime}ms`, error.message);
        this.$message.error("文件上传测试失败: " + error.message);
      } finally {
        this.loading.upload = false;
      }
    },

    // 更新测试结果汇总
    updateSummary(testName, status, time, error = "-") {
      const index = this.summaryData.findIndex(item => item.test === testName);
      if (index !== -1) {
        this.summaryData[index].status = status;
        this.summaryData[index].time = time;
        this.summaryData[index].error = error;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.test-container {
  padding: 20px;
  background: #fff;
  border-radius: 4px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;

  h3 {
    margin-bottom: 16px;
    color: #1890ff;
  }
}

.result-box {
  margin-top: 16px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;

  pre {
    margin: 0;
    font-size: 12px;
    line-height: 1.4;
  }
}
</style>
